<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.SherpaOnnx2Pass"
        tools:targetApi="31">
        <!-- Apple-inspired Voice Assistant Activity -->
        <activity
            android:name=".VoiceAssistantActivity"
            android:exported="true"
            android:label="@string/app_name_optimized"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.VoiceAssistant"
            android:windowSoftInputMode="adjustResize">
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->
<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--            </intent-filter>-->
        </activity>

        <!-- Legacy Single Model Activity (for compatibility) -->
        <activity
            android:name=".SingleModelActivity"
            android:exported="true"
            android:label="高效语音识别"
            android:theme="@style/Theme.SherpaOnnx2Pass.Optimized">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- LLM设置页面 -->
        <activity
            android:name=".SettingsActivity"
            android:exported="false"
            android:label="LLM 设置"
            android:parentActivityName=".SingleModelActivity"
            android:theme="@style/Theme.SherpaOnnx2Pass">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".SingleModelActivity" />
        </activity>
    </application>

</manifest>
